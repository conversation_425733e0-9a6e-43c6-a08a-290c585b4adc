import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import { z } from 'zod'

const assignTestSchema = z.object({
  groupId: z.string().optional(),
  studentIds: z.array(z.string()).optional(),
  assignmentType: z.enum(['GROUP', 'INDIVIDUAL']),
  dueDate: z.string().optional(),
  instructions: z.string().optional(),
})

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can assign tests
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = assignTestSchema.parse(body)

    // Check if template exists
    const template = await prisma.testTemplate.findUnique({
      where: { id: params.id },
    })

    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 })
    }

    const assignments = []

    if (validatedData.assignmentType === 'GROUP' && validatedData.groupId) {
      // Assign to all students in the group
      const group = await prisma.group.findUnique({
        where: { id: validatedData.groupId },
        include: {
          enrollments: {
            where: { status: 'ACTIVE' },
            include: { student: true }
          }
        }
      })

      if (!group) {
        return NextResponse.json({ error: 'Group not found' }, { status: 404 })
      }

      // Create assessments for each student in the group
      for (const enrollment of group.enrollments) {
        const assessment = await prisma.assessment.create({
          data: {
            studentId: enrollment.student.id,
            groupId: validatedData.groupId,
            templateId: params.id,
            type: template.type,
            level: template.level,
            maxScore: template.maxScore,
            timeLimit: template.timeLimit,
            assignedBy: session.user.id,
            assignedAt: new Date(),
            status: 'ASSIGNED',
            questions: template.questions as any,
          },
        })
        assignments.push(assessment)
      }

      // Log the activity
      await ActivityLogger.log({
        userId: session.user.id,
        userRole: session.user.role as Role,
        action: 'CREATE',
        resource: 'assessment',
        resourceId: validatedData.groupId,
        details: {
          templateTitle: template.title,
          groupName: group.name,
          studentsCount: group.enrollments.length,
          assignmentType: 'GROUP',
        },
        ipAddress: ActivityLogger.getIpAddress(request),
        userAgent: ActivityLogger.getUserAgent(request),
      })

    } else if (validatedData.assignmentType === 'INDIVIDUAL' && validatedData.studentIds) {
      // Assign to specific students
      for (const studentId of validatedData.studentIds) {
        const assessment = await prisma.assessment.create({
          data: {
            studentId,
            templateId: params.id,
            type: template.type,
            level: template.level,
            maxScore: template.maxScore,
            timeLimit: template.timeLimit,
            assignedBy: session.user.id,
            assignedAt: new Date(),
            status: 'ASSIGNED',
            questions: template.questions as any,
          },
        })
        assignments.push(assessment)
      }

      // Log the activity
      await ActivityLogger.log({
        userId: session.user.id,
        userRole: session.user.role as Role,
        action: 'CREATE',
        resource: 'assessment',
        resourceId: params.id,
        details: {
          templateTitle: template.title,
          studentsCount: validatedData.studentIds.length,
          assignmentType: 'INDIVIDUAL',
        },
        ipAddress: ActivityLogger.getIpAddress(request),
        userAgent: ActivityLogger.getUserAgent(request),
      })
    }

    return NextResponse.json({ 
      message: 'Test assigned successfully',
      assignments: assignments.length,
      assessmentIds: assignments.map(a => a.id)
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error assigning test:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

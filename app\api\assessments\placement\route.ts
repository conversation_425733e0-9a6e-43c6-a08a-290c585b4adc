import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import { z } from 'zod'

const placementTestSchema = z.object({
  studentId: z.string(),
  answers: z.array(z.object({
    questionId: z.string(),
    answer: z.any(),
    timeSpent: z.number().optional(),
  })),
  currentDifficulty: z.number().min(1).max(10),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = placementTestSchema.parse(body)

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: validatedData.studentId },
      include: { user: true }
    })

    if (!student) {
      return NextResponse.json({ error: 'Student not found' }, { status: 404 })
    }

    // Check if student already has a placement test
    const existingTest = await prisma.assessment.findFirst({
      where: {
        studentId: validatedData.studentId,
        type: 'PLACEMENT_TEST',
        status: { in: ['ASSIGNED', 'IN_PROGRESS'] }
      }
    })

    if (existingTest) {
      // Update existing test with new answers
      const updatedTest = await updateAdaptiveTest(existingTest, validatedData)
      return NextResponse.json(updatedTest)
    } else {
      // Create new placement test
      const newTest = await createAdaptivePlacementTest(validatedData, session.user.id, request)
      return NextResponse.json(newTest)
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error processing placement test:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function createAdaptivePlacementTest(data: any, userId: string, request: NextRequest) {
  // Get placement test template
  const template = await prisma.testTemplate.findFirst({
    where: {
      type: 'PLACEMENT_TEST',
      isAdaptive: true,
      isActive: true
    }
  })

  if (!template) {
    throw new Error('No adaptive placement test template found')
  }

  // Calculate initial score and determine next difficulty
  const { score, nextDifficulty, suggestedLevel } = calculateAdaptiveScore(
    data.answers,
    data.currentDifficulty,
    template.questions
  )

  const assessment = await prisma.assessment.create({
    data: {
      studentId: data.studentId,
      templateId: template.id,
      type: 'PLACEMENT_TEST',
      isAdaptive: true,
      difficulty: nextDifficulty,
      score,
      maxScore: template.maxScore,
      passed: score >= template.passScore,
      questions: template.questions as any,
      results: {
        answers: data.answers,
        suggestedLevel,
        difficultyProgression: [data.currentDifficulty, nextDifficulty]
      } as any,
      status: nextDifficulty <= 10 ? 'IN_PROGRESS' : 'COMPLETED',
      startedAt: new Date(),
      completedAt: nextDifficulty > 10 ? new Date() : null,
    }
  })

  // Log the activity
  await ActivityLogger.log({
    userId,
    userRole: 'STUDENT' as Role,
    action: 'CREATE',
    resource: 'assessment',
    resourceId: assessment.id,
    details: {
      type: 'PLACEMENT_TEST',
      studentName: data.studentId,
      difficulty: nextDifficulty,
      score,
      suggestedLevel,
    },
    ipAddress: ActivityLogger.getIpAddress(request),
    userAgent: ActivityLogger.getUserAgent(request),
  })

  return {
    assessmentId: assessment.id,
    currentDifficulty: nextDifficulty,
    score,
    suggestedLevel,
    isComplete: nextDifficulty > 10,
    nextQuestions: nextDifficulty <= 10 ? getQuestionsForDifficulty(template.questions, nextDifficulty) : null
  }
}

async function updateAdaptiveTest(existingTest: any, data: any) {
  // Calculate new score and determine next difficulty
  const currentResults = existingTest.results || { answers: [], difficultyProgression: [] }
  const allAnswers = [...currentResults.answers, ...data.answers]
  
  const { score, nextDifficulty, suggestedLevel } = calculateAdaptiveScore(
    allAnswers,
    data.currentDifficulty,
    existingTest.questions
  )

  const updatedTest = await prisma.assessment.update({
    where: { id: existingTest.id },
    data: {
      difficulty: nextDifficulty,
      score,
      passed: score >= (existingTest.maxScore * 0.7), // 70% pass rate
      results: {
        answers: allAnswers,
        suggestedLevel,
        difficultyProgression: [...currentResults.difficultyProgression, nextDifficulty]
      } as any,
      status: nextDifficulty > 10 ? 'COMPLETED' : 'IN_PROGRESS',
      completedAt: nextDifficulty > 10 ? new Date() : null,
    }
  })

  return {
    assessmentId: updatedTest.id,
    currentDifficulty: nextDifficulty,
    score,
    suggestedLevel,
    isComplete: nextDifficulty > 10,
    nextQuestions: nextDifficulty <= 10 ? getQuestionsForDifficulty(existingTest.questions, nextDifficulty) : null
  }
}

function calculateAdaptiveScore(answers: any[], currentDifficulty: number, questionBank: any) {
  // Simple adaptive algorithm - adjust based on correct answers
  const correctAnswers = answers.filter(answer => answer.isCorrect).length
  const totalAnswers = answers.length
  const accuracy = totalAnswers > 0 ? correctAnswers / totalAnswers : 0

  let nextDifficulty = currentDifficulty
  
  if (accuracy >= 0.8) {
    nextDifficulty = Math.min(10, currentDifficulty + 2)
  } else if (accuracy >= 0.6) {
    nextDifficulty = Math.min(10, currentDifficulty + 1)
  } else if (accuracy < 0.4) {
    nextDifficulty = Math.max(1, currentDifficulty - 1)
  }

  // Determine suggested level based on final difficulty
  const levelMap: { [key: number]: string } = {
    1: 'A1', 2: 'A1', 3: 'A2', 4: 'A2',
    5: 'B1', 6: 'B1', 7: 'B2', 8: 'B2',
    9: 'C1', 10: 'C2'
  }

  const suggestedLevel = levelMap[Math.min(nextDifficulty, 10)] || 'A1'
  const score = Math.round(accuracy * 100)

  return { score, nextDifficulty, suggestedLevel }
}

function getQuestionsForDifficulty(questionBank: any, difficulty: number) {
  // Return questions appropriate for the difficulty level
  if (!questionBank || !questionBank.questions) return []
  
  return questionBank.questions
    .filter((q: any) => q.difficulty === difficulty)
    .slice(0, 5) // Return 5 questions per difficulty level
}

import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 10)
  
  const adminUser = await prisma.user.upsert({
    where: { phone: '+998901234567' },
    update: {},
    create: {
      phone: '+998901234567',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'ADMIN',
      password: hashedPassword,
    },
  })

  // Create manager user
  const managerPassword = await bcrypt.hash('manager123', 10)
  
  const managerUser = await prisma.user.upsert({
    where: { phone: '+998901234568' },
    update: {},
    create: {
      phone: '+998901234568',
      name: 'Manager User',
      email: '<EMAIL>',
      role: 'MANAGER',
      password: managerPassword,
    },
  })

  // Create reception user
  const receptionPassword = await bcrypt.hash('reception123', 10)
  
  const receptionUser = await prisma.user.upsert({
    where: { phone: '+998901234569' },
    update: {},
    create: {
      phone: '+998901234569',
      name: 'Reception User',
      email: '<EMAIL>',
      role: 'RECEPTION',
      password: receptionPassword,
    },
  })

  // Create some sample courses
  const courses = [
    {
      name: 'General English A1',
      level: 'A1' as const,
      description: 'Beginner level English course',
      duration: 12,
      price: 500000,
    },
    {
      name: 'General English A2',
      level: 'A2' as const,
      description: 'Elementary level English course',
      duration: 12,
      price: 550000,
    },
    {
      name: 'IELTS 6.0 Preparation',
      level: 'IELTS_6_0' as const,
      description: 'IELTS preparation for band 6.0',
      duration: 8,
      price: 800000,
    },
    {
      name: 'Kids English Beginners',
      level: 'KIDS' as const,
      description: 'English for children aged 6-10',
      duration: 16,
      price: 400000,
    },
  ]

  for (const course of courses) {
    await prisma.course.upsert({
      where: { name: course.name },
      update: {},
      create: course,
    })
  }

  // Create some sample leads
  const leads = [
    {
      name: 'Aziza Karimova',
      phone: '+998901111111',
      coursePreference: 'IELTS Preparation',
      status: 'NEW' as const,
      source: 'Website',
    },
    {
      name: 'Bobur Toshev',
      phone: '+998902222222',
      coursePreference: 'General English',
      status: 'CONTACTED' as const,
      source: 'Website',
    },
    {
      name: 'Dilnoza Rahimova',
      phone: '+998903333333',
      coursePreference: 'Kids English',
      status: 'INTERESTED' as const,
      source: 'Website',
    },
  ]

  for (const lead of leads) {
    await prisma.lead.upsert({
      where: { phone: lead.phone },
      update: {},
      create: lead,
    })
  }

  // Create sample students
  const studentUser1 = await prisma.user.upsert({
    where: { phone: '+998904444444' },
    update: {},
    create: {
      phone: '+998904444444',
      name: 'Aziza Karimova',
      email: '<EMAIL>',
      role: 'STUDENT',
      password: await bcrypt.hash('student123', 10),
    },
  })

  const student1 = await prisma.student.upsert({
    where: { userId: studentUser1.id },
    update: {},
    create: {
      userId: studentUser1.id,
      level: 'B1' as const,
      branch: 'Main Branch',
      emergencyContact: '+998901111111',
    },
  })

  // Create sample teacher
  const teacherUser = await prisma.user.upsert({
    where: { phone: '+998905555555' },
    update: {},
    create: {
      phone: '+998905555555',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'TEACHER',
      password: await bcrypt.hash('teacher123', 10),
    },
  })

  const teacher = await prisma.teacher.upsert({
    where: { userId: teacherUser.id },
    update: {},
    create: {
      userId: teacherUser.id,
      subject: 'English',
      experience: 5,
      salary: 5000000,
      branch: 'Main Branch',
    },
  })

  // Create sample groups
  const course1 = await prisma.course.findFirst({ where: { name: 'General English A1' } })
  if (course1) {
    await prisma.group.upsert({
      where: { name: 'A1-Morning-Group' },
      update: {},
      create: {
        name: 'A1-Morning-Group',
        courseId: course1.id,
        teacherId: teacher.id,
        capacity: 20,
        schedule: 'Mon, Wed, Fri - 9:00 AM',
        room: 'Room 101',
        branch: 'Main Branch',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-04-15'),
      },
    })
  }

  // Create sample payments
  await prisma.payment.upsert({
    where: { id: 'sample-payment-1' },
    update: {},
    create: {
      id: 'sample-payment-1',
      studentId: student1.id,
      amount: 500000,
      method: 'CASH' as const,
      status: 'PAID' as const,
      description: 'Monthly tuition fee',
      paidDate: new Date(),
    },
  })

  console.log('Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

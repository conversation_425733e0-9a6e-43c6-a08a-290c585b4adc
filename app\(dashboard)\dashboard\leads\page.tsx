'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { formatDateTime } from '@/lib/utils'
import { Phone, Mail, User, Calendar } from 'lucide-react'

interface Lead {
  id: string
  name: string
  phone: string
  coursePreference: string
  status: string
  source?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState('ALL')

  const fetchLeads = useCallback(async () => {
    try {
      const url = filter === 'ALL' ? '/api/leads' : `/api/leads?status=${filter}`
      const response = await fetch(url)
      const data = await response.json()
      setLeads(data.leads || [])
    } catch (error) {
      console.error('Error fetching leads:', error)
    } finally {
      setLoading(false)
    }
  }, [filter])

  useEffect(() => {
    fetchLeads()
  }, [fetchLeads])

  const updateLeadStatus = async (leadId: string, newStatus: string) => {
    try {
      const response = await fetch(`/api/leads/${leadId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      })

      if (response.ok) {
        fetchLeads() // Refresh the list
      }
    } catch (error) {
      console.error('Error updating lead status:', error)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800'
      case 'CONTACTED':
        return 'bg-yellow-100 text-yellow-800'
      case 'INTERESTED':
        return 'bg-green-100 text-green-800'
      case 'ENROLLED':
        return 'bg-purple-100 text-purple-800'
      case 'NOT_INTERESTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leads Management</h1>
          <p className="text-gray-600">Manage and track potential student inquiries</p>
        </div>
      </div>

      {/* Filter Buttons */}
      <div className="flex space-x-2">
        {['ALL', 'NEW', 'CONTACTED', 'INTERESTED', 'ENROLLED', 'NOT_INTERESTED'].map((status) => (
          <Button
            key={status}
            variant={filter === status ? 'default' : 'outline'}
            onClick={() => setFilter(status)}
            size="sm"
          >
            {status.replace('_', ' ')}
          </Button>
        ))}
      </div>

      {/* Leads Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {leads.map((lead) => (
          <Card key={lead.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg flex items-center">
                    <User className="h-4 w-4 mr-2" />
                    {lead.name}
                  </CardTitle>
                  <CardDescription className="flex items-center mt-1">
                    <Phone className="h-3 w-3 mr-1" />
                    {lead.phone}
                  </CardDescription>
                </div>
                <Badge className={getStatusColor(lead.status)}>
                  {lead.status.replace('_', ' ')}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Course Interest:</p>
                  <p className="text-sm text-gray-600">{lead.coursePreference}</p>
                </div>
                
                {lead.source && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Source:</p>
                    <p className="text-sm text-gray-600">{lead.source}</p>
                  </div>
                )}

                <div className="flex items-center text-xs text-gray-500">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatDateTime(new Date(lead.createdAt))}
                </div>

                {lead.notes && (
                  <div>
                    <p className="text-sm font-medium text-gray-700">Notes:</p>
                    <p className="text-sm text-gray-600">{lead.notes}</p>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex space-x-2 pt-2">
                  {lead.status === 'NEW' && (
                    <Button
                      size="sm"
                      onClick={() => updateLeadStatus(lead.id, 'CONTACTED')}
                      className="flex-1"
                    >
                      Mark Contacted
                    </Button>
                  )}
                  {lead.status === 'CONTACTED' && (
                    <>
                      <Button
                        size="sm"
                        onClick={() => updateLeadStatus(lead.id, 'INTERESTED')}
                        className="flex-1"
                      >
                        Interested
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateLeadStatus(lead.id, 'NOT_INTERESTED')}
                        className="flex-1"
                      >
                        Not Interested
                      </Button>
                    </>
                  )}
                  {lead.status === 'INTERESTED' && (
                    <Button
                      size="sm"
                      onClick={() => updateLeadStatus(lead.id, 'ENROLLED')}
                      className="flex-1"
                    >
                      Mark Enrolled
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {leads.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No leads found for the selected filter.</p>
        </div>
      )}
    </div>
  )
}

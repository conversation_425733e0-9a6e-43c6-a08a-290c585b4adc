import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ActivityFeed } from '@/components/dashboard/activity-feed'
import { Users, UserPlus, GraduationCap, DollarSign } from 'lucide-react'

export default function DashboardPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to Innovative Centre CRM</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4,247</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">New Leads</CardTitle>
            <UserPlus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">127</div>
            <p className="text-xs text-muted-foreground">
              +8% from last week
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">
              +3 new groups this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2.4B UZS</div>
            <p className="text-xs text-muted-foreground">
              +15% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Leads</CardTitle>
            <CardDescription>Latest inquiries from potential students</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentLeads.map((lead, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{lead.name}</p>
                    <p className="text-sm text-gray-600">{lead.course}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-600">{lead.time}</p>
                    <span className={`inline-flex px-2 py-1 text-xs rounded-full ${
                      lead.status === 'NEW' ? 'bg-blue-100 text-blue-800' :
                      lead.status === 'CONTACTED' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {lead.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Upcoming Classes</CardTitle>
            <CardDescription>Today&apos;s scheduled classes</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingClasses.map((classItem, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{classItem.group}</p>
                    <p className="text-sm text-gray-600">{classItem.teacher}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{classItem.time}</p>
                    <p className="text-sm text-gray-600">{classItem.room}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <ActivityFeed
          limit={8}
          showHeader={true}
          showRefresh={true}
          showViewAll={true}
        />
      </div>
    </div>
  )
}

const recentLeads = [
  { name: 'Aziza Karimova', course: 'IELTS Preparation', time: '2 hours ago', status: 'NEW' },
  { name: 'Bobur Toshev', course: 'General English', time: '4 hours ago', status: 'CONTACTED' },
  { name: 'Dilnoza Rahimova', course: 'Kids English', time: '6 hours ago', status: 'INTERESTED' },
  { name: 'Eldor Nazarov', course: 'SAT Preparation', time: '1 day ago', status: 'NEW' },
]

const upcomingClasses = [
  { group: 'IELTS 6.5 - Group A', teacher: 'Ms. Sarah', time: '10:00 AM', room: 'Room 101' },
  { group: 'General B1 - Group C', teacher: 'Mr. John', time: '11:30 AM', room: 'Room 203' },
  { group: 'Kids English - Beginners', teacher: 'Ms. Anna', time: '2:00 PM', room: 'Room 105' },
  { group: 'SAT Math', teacher: 'Mr. David', time: '4:00 PM', room: 'Room 301' },
]

'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { useToast } from '@/hooks/use-toast'
import { Plus, Search, Edit, Trash2, Users, Clock, Target } from 'lucide-react'

interface TestTemplate {
  id: string
  title: string
  description?: string
  type: string
  level?: string
  timeLimit?: number
  maxScore: number
  passScore: number
  isActive: boolean
  isAdaptive: boolean
  createdAt: string
  _count?: {
    assessments: number
  }
}

export default function TestTemplatesPage() {
  const [templates, setTemplates] = useState<TestTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<TestTemplate | null>(null)
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'LEVEL_TEST',
    level: '',
    timeLimit: 60,
    maxScore: 100,
    passScore: 70,
    isActive: true,
    isAdaptive: false,
    questions: { questions: [] }
  })

  useEffect(() => {
    fetchTemplates()
  }, [selectedType])

  const fetchTemplates = async () => {
    try {
      const params = new URLSearchParams()
      if (selectedType && selectedType !== 'all') params.append('type', selectedType)

      const response = await fetch(`/api/test-templates?${params}`)
      if (!response.ok) throw new Error('Failed to fetch templates')

      const data = await response.json()
      setTemplates(data.templates)
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast({
        title: 'Error',
        description: 'Failed to fetch test templates',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const url = editingTemplate ? `/api/test-templates/${editingTemplate.id}` : '/api/test-templates'
      const method = editingTemplate ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) throw new Error('Failed to save template')

      toast({
        title: 'Success',
        description: `Template ${editingTemplate ? 'updated' : 'created'} successfully`,
      })

      setIsCreateDialogOpen(false)
      setEditingTemplate(null)
      resetForm()
      fetchTemplates()
    } catch (error) {
      console.error('Error saving template:', error)
      toast({
        title: 'Error',
        description: 'Failed to save template',
        variant: 'destructive',
      })
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this template?')) return

    try {
      const response = await fetch(`/api/test-templates/${id}`, {
        method: 'DELETE',
      })

      if (!response.ok) throw new Error('Failed to delete template')

      toast({
        title: 'Success',
        description: 'Template deleted successfully',
      })

      fetchTemplates()
    } catch (error) {
      console.error('Error deleting template:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete template',
        variant: 'destructive',
      })
    }
  }

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'LEVEL_TEST',
      level: '',
      timeLimit: 60,
      maxScore: 100,
      passScore: 70,
      isActive: true,
      isAdaptive: false,
      questions: { questions: [] }
    })
  }

  const openEditDialog = (template: TestTemplate) => {
    setEditingTemplate(template)
    setFormData({
      title: template.title,
      description: template.description || '',
      type: template.type,
      level: template.level || '',
      timeLimit: template.timeLimit || 60,
      maxScore: template.maxScore,
      passScore: template.passScore,
      isActive: template.isActive,
      isAdaptive: template.isAdaptive,
      questions: { questions: [] }
    })
    setIsCreateDialogOpen(true)
  }

  const filteredTemplates = templates.filter(template =>
    template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      'PLACEMENT_TEST': 'bg-blue-100 text-blue-800',
      'LEVEL_TEST': 'bg-green-100 text-green-800',
      'PROGRESS_TEST': 'bg-yellow-100 text-yellow-800',
      'FINAL_EXAM': 'bg-red-100 text-red-800',
      'MOCK_TEST': 'bg-purple-100 text-purple-800',
      'GROUP_TEST': 'bg-indigo-100 text-indigo-800',
    }
    return colors[type] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Test Templates</h1>
          <p className="text-muted-foreground">
            Manage test templates for assessments and group tests
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" />
              Create Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>
                {editingTemplate ? 'Edit Template' : 'Create Test Template'}
              </DialogTitle>
              <DialogDescription>
                Create a reusable test template that can be assigned to students or groups.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="type">Type</Label>
                  <Select value={formData.type} onValueChange={(value) => setFormData({ ...formData, type: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLACEMENT_TEST">Placement Test</SelectItem>
                      <SelectItem value="LEVEL_TEST">Level Test</SelectItem>
                      <SelectItem value="PROGRESS_TEST">Progress Test</SelectItem>
                      <SelectItem value="FINAL_EXAM">Final Exam</SelectItem>
                      <SelectItem value="MOCK_TEST">Mock Test</SelectItem>
                      <SelectItem value="GROUP_TEST">Group Test</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="level">Level</Label>
                  <Select value={formData.level} onValueChange={(value) => setFormData({ ...formData, level: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A1">A1</SelectItem>
                      <SelectItem value="A2">A2</SelectItem>
                      <SelectItem value="B1">B1</SelectItem>
                      <SelectItem value="B2">B2</SelectItem>
                      <SelectItem value="C1">C1</SelectItem>
                      <SelectItem value="C2">C2</SelectItem>
                      <SelectItem value="IELTS_5_5">IELTS 5.5</SelectItem>
                      <SelectItem value="IELTS_6_0">IELTS 6.0</SelectItem>
                      <SelectItem value="IELTS_6_5">IELTS 6.5</SelectItem>
                      <SelectItem value="IELTS_7_0">IELTS 7.0</SelectItem>
                      <SelectItem value="SAT">SAT</SelectItem>
                      <SelectItem value="MATH">Math</SelectItem>
                      <SelectItem value="KIDS">Kids</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
                  <Input
                    id="timeLimit"
                    type="number"
                    value={formData.timeLimit}
                    onChange={(e) => setFormData({ ...formData, timeLimit: parseInt(e.target.value) })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxScore">Max Score</Label>
                  <Input
                    id="maxScore"
                    type="number"
                    value={formData.maxScore}
                    onChange={(e) => setFormData({ ...formData, maxScore: parseInt(e.target.value) })}
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="passScore">Pass Score</Label>
                  <Input
                    id="passScore"
                    type="number"
                    value={formData.passScore}
                    onChange={(e) => setFormData({ ...formData, passScore: parseInt(e.target.value) })}
                    required
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <Label htmlFor="isActive">Active</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isAdaptive"
                    checked={formData.isAdaptive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isAdaptive: checked })}
                  />
                  <Label htmlFor="isAdaptive">Adaptive</Label>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingTemplate ? 'Update' : 'Create'} Template
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="All Types" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="PLACEMENT_TEST">Placement Test</SelectItem>
            <SelectItem value="LEVEL_TEST">Level Test</SelectItem>
            <SelectItem value="PROGRESS_TEST">Progress Test</SelectItem>
            <SelectItem value="FINAL_EXAM">Final Exam</SelectItem>
            <SelectItem value="MOCK_TEST">Mock Test</SelectItem>
            <SelectItem value="GROUP_TEST">Group Test</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Templates Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg">{template.title}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge className={getTypeColor(template.type)}>
                      {template.type.replace('_', ' ')}
                    </Badge>
                    {template.isAdaptive && (
                      <Badge variant="outline">Adaptive</Badge>
                    )}
                    {!template.isActive && (
                      <Badge variant="secondary">Inactive</Badge>
                    )}
                  </div>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => openEditDialog(template)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(template.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {template.description && (
                <p className="text-sm text-muted-foreground mb-3">
                  {template.description}
                </p>
              )}
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span>{template.timeLimit || 'No'} time limit</span>
                </div>
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-muted-foreground" />
                  <span>{template.passScore}/{template.maxScore} to pass</span>
                </div>
                {template._count && (
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-muted-foreground" />
                    <span>{template._count.assessments} assessments</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No test templates found.</p>
        </div>
      )}
    </div>
  )
}

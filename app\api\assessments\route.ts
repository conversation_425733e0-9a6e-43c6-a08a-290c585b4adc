import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ActivityLogger } from '@/lib/activity-logger'
import { Role } from '@prisma/client'
import { z } from 'zod'

const assessmentSchema = z.object({
  studentId: z.string().optional(),
  groupId: z.string().optional(),
  testName: z.string().min(1, 'Test name is required'),
  type: z.enum(['LEVEL_TEST', 'PROGRESS_TEST', 'FINAL_EXAM', 'GROUP_TEST']),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'IELTS_5_5', 'IELTS_6_0', 'IELTS_6_5', 'IELTS_7_0', 'SAT', 'MATH', 'KIDS']).optional(),
  score: z.number().optional(),
  maxScore: z.number().optional(),
  passed: z.boolean().default(false),
  questions: z.any().optional(),
  results: z.any().optional(),
  completedAt: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const studentId = searchParams.get('studentId')
    const type = searchParams.get('type')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')

    const where: any = {}
    if (studentId) where.studentId = studentId
    if (type) where.type = type

    const assessments = await prisma.assessment.findMany({
      where,
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    })

    return NextResponse.json({
      assessments,
    })
  } catch (error) {
    console.error('Error fetching assessments:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can create assessments
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = assessmentSchema.parse(body)

    // Check if student exists (if studentId provided)
    if (validatedData.studentId) {
      const student = await prisma.student.findUnique({
        where: { id: validatedData.studentId },
      })

      if (!student) {
        return NextResponse.json({ error: 'Student not found' }, { status: 404 })
      }
    }

    const assessment = await prisma.assessment.create({
      data: {
        ...validatedData,
        completedAt: validatedData.completedAt ? new Date(validatedData.completedAt) : null,
      },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        group: {
          include: {
            course: {
              select: {
                name: true,
                level: true,
              },
            },
          },
        },
      },
    })

    // Log the activity
    await ActivityLogger.logAssessmentCompleted(
      session.user.id,
      session.user.role as Role,
      assessment.id,
      {
        type: assessment.type,
        studentName: assessment.student?.user.name || 'Unknown',
        score: assessment.score,
        passed: assessment.passed,
      },
      request
    )

    return NextResponse.json(assessment, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating assessment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

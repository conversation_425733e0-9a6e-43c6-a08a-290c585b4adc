import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { Role } from '@prisma/client'
import { z } from 'zod'

const testTemplateSchema = z.object({
  title: z.string().min(1),
  description: z.string().optional(),
  type: z.enum(['PLACEMENT_TEST', 'LEVEL_TEST', 'PROGRESS_TEST', 'FINAL_EXAM', 'MOCK_TEST', 'GROUP_TEST']),
  level: z.enum(['A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'IELTS_5_5', 'IELTS_6_0', 'IELTS_6_5', 'IELTS_7_0', 'SAT', 'MATH', 'KIDS']).optional(),
  questions: z.any(), // JSON structure for questions
  timeLimit: z.number().optional(),
  maxScore: z.number(),
  passScore: z.number(),
  isAdaptive: z.boolean().default(false),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const level = searchParams.get('level')
    const isActive = searchParams.get('isActive')

    const where: any = {}
    if (type) where.type = type
    if (level) where.level = level
    if (isActive !== null) where.isActive = isActive === 'true'

    const templates = await prisma.testTemplate.findMany({
      where,
      orderBy: { createdAt: 'desc' },
    })

    return NextResponse.json({ templates })
  } catch (error) {
    console.error('Error fetching test templates:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only teachers, admins, and managers can create test templates
    if (!session.user.role || !['ADMIN', 'MANAGER', 'TEACHER'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = testTemplateSchema.parse(body)

    const template = await prisma.testTemplate.create({
      data: {
        ...validatedData,
        questions: validatedData.questions as any,
        createdBy: session.user.id,
      },
    })

    return NextResponse.json(template, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating test template:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import * as z from 'zod'

const updatePaymentSchema = z.object({
  amount: z.number().min(0).optional(),
  method: z.enum(['CASH', 'UZCARD', 'HUMO', 'PAYME', 'CLICK', 'BANK_TRANSFER']).optional(),
  status: z.enum(['PENDING', 'PAID', 'OVERDUE', 'REFUNDED']).optional(),
  description: z.string().optional(),
  transactionId: z.string().optional(),
  dueDate: z.string().optional(),
  paidDate: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const payment = await prisma.payment.findUnique({
      where: { id: params.id },
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
            enrollments: {
              include: {
                group: {
                  include: {
                    course: {
                      select: {
                        name: true,
                        level: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    })

    if (!payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(payment)
  } catch (error) {
    console.error('Error fetching payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const validatedData = updatePaymentSchema.parse(body)

    // Check if payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id: params.id },
    })

    if (!existingPayment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }

    // If status is being changed to PAID, set paidDate automatically
    const updateData: any = {
      ...validatedData,
      updatedAt: new Date(),
    }

    if (validatedData.dueDate) {
      updateData.dueDate = new Date(validatedData.dueDate)
    }

    if (validatedData.paidDate) {
      updateData.paidDate = new Date(validatedData.paidDate)
    }

    // Auto-set paidDate when status changes to PAID
    if (validatedData.status === 'PAID' && !validatedData.paidDate) {
      updateData.paidDate = new Date()
    }

    // Clear paidDate if status is not PAID
    if (validatedData.status && validatedData.status !== 'PAID') {
      updateData.paidDate = null
    }

    const payment = await prisma.payment.update({
      where: { id: params.id },
      data: updateData,
      include: {
        student: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                phone: true,
                email: true,
              },
            },
          },
        },
      },
    })

    return NextResponse.json(payment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check if payment exists
    const existingPayment = await prisma.payment.findUnique({
      where: { id: params.id },
    })

    if (!existingPayment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      )
    }

    // Check if payment is already paid - might want to prevent deletion
    if (existingPayment.status === 'PAID') {
      return NextResponse.json(
        { 
          error: 'Cannot delete paid payment',
          details: 'Paid payments cannot be deleted for audit purposes. Consider refunding instead.'
        },
        { status: 400 }
      )
    }

    // Delete payment
    await prisma.payment.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ 
      message: 'Payment deleted successfully',
      deletedId: params.id 
    })
  } catch (error) {
    console.error('Error deleting payment:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

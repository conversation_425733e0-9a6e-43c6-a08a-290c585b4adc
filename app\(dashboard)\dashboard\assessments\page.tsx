'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Textarea } from '@/components/ui/textarea'
import { formatDate } from '@/lib/utils'
import { Search, Plus, FileText, CheckCircle, XCircle, Clock, BookOpen, Award } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Assessment {
  id: string
  studentId: string
  type: string
  level?: string
  score?: number
  maxScore?: number
  passed: boolean
  questions?: any
  results?: any
  completedAt?: string
  createdAt: string
  student: {
    id: string
    user: {
      id: string
      name: string
      email?: string
    }
  }
}

interface AssessmentsResponse {
  assessments: Assessment[]
  total: number
  page: number
  limit: number
  totalPages: number
}

interface Student {
  id: string
  user: {
    id: string
    name: string
    email?: string
  }
}

export default function AssessmentsPage() {
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [students, setStudents] = useState<Student[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [total, setTotal] = useState(0)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [formData, setFormData] = useState({
    studentId: '',
    type: '',
    level: '',
    score: '',
    maxScore: '',
    passed: false,
    questions: '',
    results: '',
    completedAt: '',
  })
  const { toast } = useToast()

  const fetchAssessments = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      })

      if (selectedType && selectedType !== 'all') params.append('type', selectedType)

      const response = await fetch(`/api/assessments?${params}`)
      if (!response.ok) throw new Error('Failed to fetch assessments')

      const data: AssessmentsResponse = await response.json()
      setAssessments(data.assessments)
      setTotalPages(data.totalPages)
      setTotal(data.total)
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch assessments",
      })
    } finally {
      setLoading(false)
    }
  }, [currentPage, selectedType, toast])

  const fetchStudents = async () => {
    try {
      const response = await fetch('/api/students')
      if (!response.ok) throw new Error('Failed to fetch students')

      const data = await response.json()
      setStudents(data.students || [])
    } catch (error) {
      console.error('Error fetching students:', error)
    }
  }

  useEffect(() => {
    fetchAssessments()
    fetchStudents()
  }, [fetchAssessments])

  const filteredAssessments = assessments.filter(assessment =>
    assessment.student.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assessment.type.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const payload = {
        ...formData,
        score: formData.score ? parseInt(formData.score) : undefined,
        maxScore: formData.maxScore ? parseInt(formData.maxScore) : undefined,
        questions: formData.questions ? JSON.parse(formData.questions) : undefined,
        results: formData.results ? JSON.parse(formData.results) : undefined,
        completedAt: formData.completedAt || new Date().toISOString(),
      }

      const response = await fetch('/api/assessments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      })

      if (!response.ok) throw new Error('Failed to create assessment')

      toast({
        title: "Success",
        description: "Assessment created successfully",
      })

      setIsCreateDialogOpen(false)
      resetForm()
      fetchAssessments()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create assessment",
      })
    }
  }

  const resetForm = () => {
    setFormData({
      studentId: '',
      type: '',
      level: '',
      score: '',
      maxScore: '',
      passed: false,
      questions: '',
      results: '',
      completedAt: '',
    })
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PLACEMENT_TEST': return 'bg-blue-100 text-blue-800'
      case 'LEVEL_TEST': return 'bg-green-100 text-green-800'
      case 'PROGRESS_TEST': return 'bg-yellow-100 text-yellow-800'
      case 'FINAL_EXAM': return 'bg-red-100 text-red-800'
      case 'MOCK_TEST': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPassedIcon = (passed: boolean) => {
    return passed ? (
      <CheckCircle className="h-5 w-5 text-green-600" />
    ) : (
      <XCircle className="h-5 w-5 text-red-600" />
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Assessments</h1>
          <p className="text-gray-600">Manage student assessments and test results</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="h-4 w-4 mr-2" />
              Create Assessment
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Assessment</DialogTitle>
              <DialogDescription>
                Create a new assessment for a student.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Student *</label>
                  <Select value={formData.studentId} onValueChange={(value) => setFormData({...formData, studentId: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select student" />
                    </SelectTrigger>
                    <SelectContent>
                      {students.map((student) => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.user.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Assessment Type *</label>
                  <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="PLACEMENT_TEST">Placement Test</SelectItem>
                      <SelectItem value="LEVEL_TEST">Level Test</SelectItem>
                      <SelectItem value="PROGRESS_TEST">Progress Test</SelectItem>
                      <SelectItem value="FINAL_EXAM">Final Exam</SelectItem>
                      <SelectItem value="MOCK_TEST">Mock Test</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Level</label>
                  <Select value={formData.level} onValueChange={(value) => setFormData({...formData, level: value})}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A1">A1</SelectItem>
                      <SelectItem value="A2">A2</SelectItem>
                      <SelectItem value="B1">B1</SelectItem>
                      <SelectItem value="B2">B2</SelectItem>
                      <SelectItem value="C1">C1</SelectItem>
                      <SelectItem value="C2">C2</SelectItem>
                      <SelectItem value="IELTS_5_5">IELTS 5.5</SelectItem>
                      <SelectItem value="IELTS_6_0">IELTS 6.0</SelectItem>
                      <SelectItem value="IELTS_6_5">IELTS 6.5</SelectItem>
                      <SelectItem value="IELTS_7_0">IELTS 7.0+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium">Completed At</label>
                  <Input
                    type="datetime-local"
                    value={formData.completedAt}
                    onChange={(e) => setFormData({...formData, completedAt: e.target.value})}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Score</label>
                  <Input
                    type="number"
                    value={formData.score}
                    onChange={(e) => setFormData({...formData, score: e.target.value})}
                    placeholder="Enter score"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Max Score</label>
                  <Input
                    type="number"
                    value={formData.maxScore}
                    onChange={(e) => setFormData({...formData, maxScore: e.target.value})}
                    placeholder="Enter max score"
                  />
                </div>
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.passed}
                    onChange={(e) => setFormData({...formData, passed: e.target.checked})}
                    className="rounded"
                  />
                  <span className="text-sm font-medium">Passed</span>
                </label>
              </div>

              <div>
                <label className="text-sm font-medium">Questions (JSON)</label>
                <Textarea
                  value={formData.questions}
                  onChange={(e) => setFormData({...formData, questions: e.target.value})}
                  placeholder='{"questions": [{"question": "What is...?", "answer": "..."}]}'
                  rows={3}
                />
              </div>

              <div>
                <label className="text-sm font-medium">Results (JSON)</label>
                <Textarea
                  value={formData.results}
                  onChange={(e) => setFormData({...formData, results: e.target.value})}
                  placeholder='{"breakdown": {"reading": 85, "writing": 78}}'
                  rows={3}
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Create Assessment</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Assessments</p>
                <p className="text-2xl font-bold text-gray-900">{total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Passed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {assessments.filter(a => a.passed).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <XCircle className="h-8 w-8 text-red-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Failed</p>
                <p className="text-2xl font-bold text-gray-900">
                  {assessments.filter(a => !a.passed).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pass Rate</p>
                <p className="text-2xl font-bold text-gray-900">
                  {assessments.length > 0 
                    ? Math.round((assessments.filter(a => a.passed).length / assessments.length) * 100)
                    : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter assessments by type and search</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search students, types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="PLACEMENT_TEST">Placement Test</SelectItem>
                <SelectItem value="LEVEL_TEST">Level Test</SelectItem>
                <SelectItem value="PROGRESS_TEST">Progress Test</SelectItem>
                <SelectItem value="FINAL_EXAM">Final Exam</SelectItem>
                <SelectItem value="MOCK_TEST">Mock Test</SelectItem>
              </SelectContent>
            </Select>

            <Button onClick={fetchAssessments} variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Assessments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Assessments</CardTitle>
          <CardDescription>
            Showing {filteredAssessments.length} of {total} assessments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Score</TableHead>
                    <TableHead>Result</TableHead>
                    <TableHead>Completed</TableHead>
                    <TableHead>Created</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssessments.map((assessment) => (
                    <TableRow key={assessment.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assessment.student.user.name}</div>
                          <div className="text-sm text-gray-500">{assessment.student.user.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getTypeColor(assessment.type)}>
                          {assessment.type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>{assessment.level || '-'}</TableCell>
                      <TableCell>
                        {assessment.score && assessment.maxScore 
                          ? `${assessment.score}/${assessment.maxScore}`
                          : '-'
                        }
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          {getPassedIcon(assessment.passed)}
                          <span>{assessment.passed ? 'Passed' : 'Failed'}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {assessment.completedAt ? formatDate(assessment.completedAt) : '-'}
                      </TableCell>
                      <TableCell>{formatDate(assessment.createdAt)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-between items-center mt-4">
              <div className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
